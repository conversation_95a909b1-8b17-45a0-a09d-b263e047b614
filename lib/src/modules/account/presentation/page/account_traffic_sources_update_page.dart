import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_view.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/validator/validators.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:material_symbols_icons/symbols.dart';

class AccountTrafficSourcesUpdatePage extends StatefulWidget {
  final SocialInfo trafficSource;
  const AccountTrafficSourcesUpdatePage(this.trafficSource, {super.key});

  @override
  State<AccountTrafficSourcesUpdatePage> createState() => _AccountTrafficSourcesUpdatePageState();
}

class _AccountTrafficSourcesUpdatePageState extends State<AccountTrafficSourcesUpdatePage> {
  late AccountCubit cubit = Modular.get<AccountCubit>();

  @override
  void initState() {
    super.initState();
    emitSocialInfo(widget.trafficSource);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: CommonAppBar(
          title: Text(widget.trafficSource == const SocialInfo() ? 'Add traffic sources' : widget.trafficSource.name),
          customAction: widget.trafficSource != const SocialInfo()
              ? FutureBuilder<int?>(
                  future: Modular.get<CommonCubit>().sharedPreferencesService.getCurrentSiteId(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) return Container() as Widget;
                    final isLastSource = cubit.state.trafficSources.length <= 1;
                    final isCurrentSite = widget.trafficSource.id == snapshot.data;

                    if (isLastSource || isCurrentSite) return Container() as Widget;

                    return IconButton(
                      icon: const Icon(Symbols.delete_sharp),
                      onPressed: () => _showDeleteConfirmation(context),
                    );
                  })
              : null,
        ),
        body: _buildBody());
  }

  Future<void> _showDeleteConfirmation(BuildContext context) async {
    final result = await showDialog<bool?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            widget.trafficSource.socialMediaType == SocialType.OTHER ? 'Delete Website' : 'Delete Social Media Channel',
            style: context.textBodyMedium(),
          ),
          content: RichText(
            text: TextSpan(
              style: context.textLabelLarge(),
              children: [
                const TextSpan(
                  text: 'Are you sure you want to delete ',
                ),
                TextSpan(text: widget.trafficSource.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                TextSpan(
                  text: ': \n${widget.trafficSource.url}?',
                ),
              ],
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          actions: <Widget>[
            TextButton(
              style: ButtonStyle(
                overlayColor: WidgetStateProperty.all(Colors.transparent),
              ),
              child: Text(
                'Cancel',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Colors.deepOrangeAccent,
                      fontWeight: FontWeight.w400,
                    ),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepOrangeAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              onPressed: () async {
                await _deleteTrafficSource();
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                    ),
              ),
            ),
          ],
        );
      },
    );

    if (result == true) {
      context.showSnackBar('"${widget.trafficSource.name}" is deleted successfully!', durationSecond: 3);
    }
  }

  Future<void> _deleteTrafficSource() async {
    if (mounted) {
      List<SocialInfo> trafficSources = List.from(cubit.state.trafficSources);
      await ReadContext(context).read<TrafficSourcesCubit>().deleteSocialInfo(widget.trafficSource.id!);
      trafficSources.removeWhere((source) => source.id == widget.trafficSource.id);
      cubit.updateAccount(cubit.state.copyWith(trafficSources: trafficSources));
      Modular.to.pop(true);
    }
  }

  void emitSocialInfo(SocialInfo socialInfo) {
    ReadContext(context).read<TrafficSourcesCubit>().emitSocialInfo(socialInfo);
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
            return SocialInfoTabView(
                socialInfo: socialInfo,
                onEmitSocialInfo: emitSocialInfo,
                onFollowerNumber: (item) {
                  ReadContext(context)
                      .read<TrafficSourcesCubit>()
                      .emitSocialInfo(socialInfo.copyWith(totalFollowerLevel: item));
                },
                onClearForm: () {});
          }),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TextButton(
                    onPressed: () {
                      Modular.to.pop();
                    },
                    child: Text('Cancel',
                        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507)))),
                BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
                  return ElevatedButton(
                      onPressed: isEnableSaveButton(socialInfo)
                          ? () async {
                              if (mounted) {
                                final trafficSourcesCubit = ReadContext(context).read<TrafficSourcesCubit>();
                                final result = await trafficSourcesCubit.upsertSocialInfo(socialInfo);

                                if (result) {
                                  List<SocialInfo> trafficSources = List.from(cubit.state.trafficSources);
                                  if (socialInfo.id == null) {
                                    trafficSources.insert(0, trafficSourcesCubit.state);
                                  } else {
                                    final data = trafficSourcesCubit.state;
                                    int index = trafficSources.indexWhere((element) => element.id == data.id);
                                    if (index != -1) {
                                      trafficSources[index] = data;
                                    }
                                  }
                                  cubit.updateAccount(cubit.state.copyWith(trafficSources: trafficSources));
                                  emitSocialInfo(trafficSourcesCubit.state);
                                  Modular.to.pop();
                                } else {
                                  final errorMessage = trafficSourcesCubit.state.errorMessage;
                                  if (errorMessage != null && errorMessage.isNotEmpty && mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(errorMessage),
                                        backgroundColor: Colors.red,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                }
                              }
                            }
                          : null,
                      child: Text(
                        'Save',
                        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
                      ));
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  bool isEnableSaveButton(SocialInfo socialInfo) {
    // Disable button if currently submitting
    if (socialInfo.isSubmitting) return false;

    final isNameValid = socialInfo.name.isNotEmpty;
    final isUrlValid = socialInfo.url.isNotEmpty && Validators.isValidUrl(socialInfo.url);

    if (!isNameValid || !isUrlValid) return false;

    if (ReadContext(context).read<TrafficSourcesCubit>().state == widget.trafficSource) return false;

    // Check for duplicate site names (excluding current site being edited)
    final accountCubit = Modular.get<AccountCubit>();
    final existingSites = accountCubit.state.trafficSources;
    final isDuplicate = existingSites.any(
        (site) => site.name.toLowerCase().trim() == socialInfo.name.toLowerCase().trim() && site.id != socialInfo.id);

    if (isDuplicate) return false;

    if (socialInfo.socialMediaType == SocialType.OTHER) {
      return true;
    }

    return Validators.isValidSocialUrl(socialInfo.url);
  }
}
