// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'site_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SiteStateImpl _$$SiteStateImplFromJson(Map<String, dynamic> json) =>
    _$SiteStateImpl(
      sites: (json['sites'] as List<dynamic>?)
              ?.map((e) => PublisherSite.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currentSiteId: (json['currentSiteId'] as num?)?.toInt() ?? 0,
      isSwitchingSite: json['isSwitchingSite'] as bool? ?? false,
      hasRecentSiteCreation: json['hasRecentSiteCreation'] as bool? ?? false,
      isRefreshingAfterCreation:
          json['isRefreshingAfterCreation'] as bool? ?? false,
    );

Map<String, dynamic> _$$SiteStateImplToJson(_$SiteStateImpl instance) =>
    <String, dynamic>{
      'sites': instance.sites,
      'currentSiteId': instance.currentSiteId,
      'isSwitchingSite': instance.isSwitchingSite,
      'hasRecentSiteCreation': instance.hasRecentSiteCreation,
      'isRefreshingAfterCreation': instance.isRefreshingAfterCreation,
    };
