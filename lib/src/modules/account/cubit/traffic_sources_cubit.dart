import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/cache_invalidation_service.dart';
import 'package:koc_app/src/shared/services/url_helpers.dart';
import '../../../shared/utils/handle_error.dart';

class TrafficSourcesCubit extends BaseCubit<SocialInfo> {
  final AccountRepository _accountRepository;
  TrafficSourcesCubit(this._accountRepository) : super(const SocialInfo());

  void emitSocialInfo(SocialInfo socialInfo) {
    emit(socialInfo);
  }

  void updateFollowerNumber(FollowerNumber followerNumber) {
    emit(state.copyWith(totalFollowerLevel: followerNumber));
  }

  void updateId(int id) {
    emit(state.copyWith(id: id));
  }

  void updateSocialType(SocialType socialMediaType) {
    emit(state.copyWith(socialMediaType: socialMediaType));
  }

  /// Validates if a site name is unique among existing traffic sources
  bool _isDuplicateSiteName(String name, int? excludeId) {
    final accountCubit = Modular.get<AccountCubit>();
    final existingSites = accountCubit.state.trafficSources;

    return existingSites
        .any((site) => site.name.toLowerCase().trim() == name.toLowerCase().trim() && site.id != excludeId);
  }

  Future<bool> upsertSocialInfo(SocialInfo socialInfo) async {
    // Prevent concurrent submissions
    if (state.isSubmitting) {
      return false;
    }

    try {
      // Set submitting state
      emit(state.copyWith(isSubmitting: true, errorMessage: null));

      // Validate for duplicate site names before submission
      if (_isDuplicateSiteName(socialInfo.name, socialInfo.id)) {
        emit(state.copyWith(
          errorMessage: 'A site with this name already exists. Please choose a different name.',
          isSubmitting: false,
        ));
        return false;
      }

      final type = getSocialTypeFromUrl(socialInfo.url);
      var updatedSource = socialInfo.copyWith(socialMediaType: type);
      if (type == SocialType.OTHER) {
        updatedSource = updatedSource.copyWith(
          totalFollowerLevel: FollowerNumber.EMPTY,
        );
      }
      final siteId = await _accountRepository.updateTrafficSources(updatedSource);

      // Use comprehensive cache invalidation for site creation
      await CacheInvalidationService().invalidateCacheAfterSiteCreation(siteId);

      // Mark that a site has been created for automatic refresh
      final siteCubit = Modular.get<SiteCubit>();
      siteCubit.markSiteCreated();

      emit(state.copyWith(
        id: siteId,
        socialMediaType: type,
        totalFollowerLevel: updatedSource.totalFollowerLevel,
        errorMessage: null,
        isSubmitting: false,
      ));
      return true;
    } catch (e) {
      emit(state.copyWith(isSubmitting: false));
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, isSubmitting: false)));
    }
    return false;
  }

  Future<bool> deleteSocialInfo(int id) async {
    try {
      await _accountRepository.deleteTrafficSources(id);

      // Use cache invalidation service for site deletion
      await CacheInvalidationService().invalidateSiteCache(specificSiteId: id);

      final sites = await commonCubit.sharedPreferencesService.getSites();
      final updatedSites = sites.where((site) => site.id != id).toList();
      await commonCubit.sharedPreferencesService.setSites(updatedSites);
      final siteCubit = Modular.get<SiteCubit>();
      await siteCubit.reloadSites();
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }
}
