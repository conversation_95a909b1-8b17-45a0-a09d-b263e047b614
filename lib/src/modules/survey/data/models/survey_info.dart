// ignore_for_file: constant_identifier_names
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';

part 'survey_info.freezed.dart';
part 'survey_info.g.dart';

@freezed
class AddPartnerRequest with _$AddPartnerRequest {
  const factory AddPartnerRequest(
      {required String email,
      required String countryCode,
      required String firstName,
      required String lastName,
      required String siteName,
      required String siteUrl,
      required String socialMediaType,
      String? socialMediaFollower}) = _AddPartnerRequest;

  factory AddPartnerRequest.fromJson(Map<String, dynamic> json) => _$AddPartnerRequestFromJson(json);
}

@freezed
class UserInfo with _$UserInfo {
  const factory UserInfo({
    @Default('') String firstName,
    @Default('') String lastName,
  }) = _UserInfo;

  factory UserInfo.fromJson(Map<String, Object?> json) => _$UserInfoFromJson(json);
}

@freezed
class SocialInfo extends BaseCubitState with _$SocialInfo {
  const factory SocialInfo({
    int? id,
    @Default(SocialType.OTHER) SocialType socialMediaType,
    @Default('') String url,
    @Default('') String name,
    @Default(FollowerNumber.KOC_LEVEL1) FollowerNumber totalFollowerLevel,
    String? errorMessage,
    @Default(false) bool isSubmitting,
  }) = _SocialInfo;

  factory SocialInfo.fromJson(Map<String, Object?> json) => _$SocialInfoFromJson(json);
}

@freezed
class PassionateInfo with _$PassionateInfo {
  const factory PassionateInfo({
    @Default(<PassionateItem>[]) List<PassionateItem> selectedPassionateItems,
  }) = _PassionateInfo;

  factory PassionateInfo.fromJson(Map<String, Object?> json) => _$PassionateInfoFromJson(json);
}

enum SocialType { YOUTUBE, FACEBOOK, TWITTER, INSTAGRAM, TIKTOK, OTHER }

enum Country {
  INDONESIA("ID", "+07:00", "IDR", "USD", 500),
  MALAYSIA("MY", "+07:00", "MYR", "MYR", 600),
  SINGAPORE("SG", "+08:00", "SGD", "USD", 500),
  THAILAND("TH", "+07:00", "THB", "THB", 10000),
  GLOBAL("SG", "+08:00", "USD", "USD", 500);

  final String code;
  final String offset;
  final String localCurrencyCode;
  final String internationalCurrencyCode;
  final double internationalMinimumPayment;
  const Country(
      this.code, this.offset, this.localCurrencyCode, this.internationalCurrencyCode, this.internationalMinimumPayment);
}

enum FollowerNumber {
  KOC_LEVEL1('1K - 10K'),
  KOC_LEVEL2('10K - 100K'),
  KOC_LEVEL3('100K - 1M'),
  KOC_LEVEL4('+1M'),
  EMPTY('');

  final String value;
  const FollowerNumber(this.value);
}

enum PassionateItem {
  AUTOMOTIVE("Automotive", Symbols.directions_car, 26),
  E_COMMERCE("E-commerce", Symbols.shopping_cart, 1),
  EDUCATION("Education", Symbols.school, 18),
  ENTERTAINMENT("Entertainment", Symbols.movie, 19),
  FINANCIAL("Financial", Symbols.finance_mode, 11),
  GAMES("Games", Symbols.stadia_controller, 20),
  ONLINE_SERVICE("Online Service", Symbols.network_manage, 21),
  TELECOMMUNICATION("Tele-communication", Symbols.cell_tower, 25),
  TRAVEL_LEISURE("Travel & Leisure", Symbols.travel, 22);

  final String value;
  final IconData icon;
  final int id;

  const PassionateItem(this.value, this.icon, this.id);

  static PassionateItem? fromJson(int id) {
    return PassionateItem.values.firstWhereOrNull((item) => item.id == id);
  }
}
