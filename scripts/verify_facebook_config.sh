#!/bin/bash

# Script to verify Facebook configuration in iOS Info.plist
# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

PLIST_FILE="ios/Runner/Info.plist"

echo -e "${YELLOW}Verifying Facebook configuration in Info.plist...${NC}"
echo "=================================================="

if [ ! -f "$PLIST_FILE" ]; then
  echo -e "${RED}ERROR: Info.plist file not found at $PLIST_FILE${NC}"
  exit 1
fi

# Check Facebook App ID
APP_ID=$(grep -A1 "<key>FacebookAppID</key>" "$PLIST_FILE" | grep "<string>" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
if [ "$APP_ID" = "YOUR_FACEBOOK_APP_ID" ] || [ -z "$APP_ID" ]; then
  echo -e "${RED}✗ Facebook App ID is not configured (still contains placeholder)${NC}"
else
  if [[ "$APP_ID" =~ ^[0-9]{10,20}$ ]]; then
    echo -e "${GREEN}✓ Facebook App ID is properly configured: $APP_ID${NC}"
  else
    echo -e "${YELLOW}⚠ Facebook App ID format may be invalid: $APP_ID${NC}"
  fi
fi

# Check Facebook Client Token
CLIENT_TOKEN=$(grep -A1 "<key>FacebookClientToken</key>" "$PLIST_FILE" | grep "<string>" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
if [ "$CLIENT_TOKEN" = "YOUR_FACEBOOK_CLIENT_TOKEN" ] || [ -z "$CLIENT_TOKEN" ]; then
  echo -e "${RED}✗ Facebook Client Token is not configured (still contains placeholder)${NC}"
else
  echo -e "${GREEN}✓ Facebook Client Token is configured${NC}"
fi

# Check Facebook URL schemes
URL_SCHEME_COUNT=$(grep -c "<string>fb$APP_ID</string>" "$PLIST_FILE")
if [ "$URL_SCHEME_COUNT" -gt 0 ]; then
  echo -e "${GREEN}✓ Facebook URL schemes are properly configured (found $URL_SCHEME_COUNT instances)${NC}"
else
  echo -e "${RED}✗ Facebook URL schemes are not properly configured${NC}"
fi

echo ""
echo -e "${YELLOW}To fix configuration issues, update your .env file and run: ./scripts/pre_build.sh${NC}"
